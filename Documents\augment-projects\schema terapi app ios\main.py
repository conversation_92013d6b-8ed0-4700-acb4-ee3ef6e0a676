# Required libraries to install via pip:
# pip install google-generativeai langchain-google-genai langchain chromadb pypdf python-docx

import os
import sys
from pathlib import Path
from typing import List, Optional

# Document processing imports
import PyPDF2
from docx import Document

# LangChain and AI imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain_community.vectorstores import Chroma
from langchain.chains import RetrievalQA
from langchain.schema import Document as Lang<PERSON>hainDocument

# Configuration
GOOGLE_API_KEY = "YOUR_GOOGLE_API_KEY"  # Replace with your actual API key
SOURCE_FOLDER = "kaynaklarim"  # Folder containing PDF and DOCX files
VECTOR_DB_PATH = "chroma_db"  # Path for persistent ChromaDB storage

def setup_environment():
    """Set up the environment with the Google API key."""
    if GOOGLE_API_KEY == "AIzaSyB8F2HEMpUToN6L8AZsOe4nW50xx2JCSlw":
        print("⚠️  Warning: Please replace 'YOUR_GOOGLE_API_KEY' with your actual Google API key!")
        print("   You can get one from: https://makersuite.google.com/app/apikey")
        return False
    
    os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
    return True

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extract text from a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"❌ Error reading PDF {pdf_path}: {str(e)}")
        return ""

def extract_text_from_docx(docx_path: str) -> str:
    """Extract text from a DOCX file."""
    try:
        doc = Document(docx_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        print(f"❌ Error reading DOCX {docx_path}: {str(e)}")
        return ""

def load_documents_from_folder(folder_path: str) -> str:
    """
    Load and extract text from all PDF and DOCX files in the specified folder.
    Returns concatenated text from all documents.
    """
    if not os.path.exists(folder_path):
        print(f"❌ Source folder '{folder_path}' does not exist!")
        print(f"   Please create the folder and add your Schema Therapy documents.")
        return ""
    
    all_text = ""
    supported_extensions = ['.pdf', '.docx']
    files_processed = 0
    
    print(f"📂 Loading documents from '{folder_path}'...")
    
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        file_extension = Path(filename).suffix.lower()
        
        if file_extension in supported_extensions and os.path.isfile(file_path):
            print(f"📄 Processing: {filename}")
            
            if file_extension == '.pdf':
                text = extract_text_from_pdf(file_path)
            elif file_extension == '.docx':
                text = extract_text_from_docx(file_path)
            
            if text.strip():
                all_text += f"\n--- Content from {filename} ---\n{text}\n"
                files_processed += 1
            else:
                print(f"⚠️  No text extracted from {filename}")
    
    print(f"✅ Successfully processed {files_processed} documents")
    print(f"📊 Total text length: {len(all_text)} characters")
    
    return all_text

def create_vector_database(text_content: str, db_path: str) -> bool:
    """
    Create a ChromaDB vector database from the provided text content.
    Returns True if successful, False otherwise.
    """
    if not text_content.strip():
        print("❌ No text content provided for vector database creation!")
        return False
    
    try:
        print("🔄 Creating vector database...")
        
        # Split text into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        print("📝 Splitting text into chunks...")
        chunks = text_splitter.split_text(text_content)
        print(f"📊 Created {len(chunks)} text chunks")
        
        # Create LangChain documents
        documents = [LangChainDocument(page_content=chunk) for chunk in chunks]
        
        # Initialize embeddings
        print("🤖 Initializing Google embeddings...")
        embeddings = GoogleGenerativeAIEmbeddings(model="models/text-embedding-004")
        
        # Create and persist vector store
        print("💾 Creating and saving vector database...")
        vectorstore = Chroma.from_documents(
            documents=documents,
            embedding=embeddings,
            persist_directory=db_path
        )
        
        print(f"✅ Vector database created successfully at '{db_path}'")
        return True
        
    except Exception as e:
        print(f"❌ Error creating vector database: {str(e)}")
        return False

def setup_qa_chain(db_path: str) -> Optional[RetrievalQA]:
    """
    Set up the Question-Answering chain with the vector database.
    Returns the QA chain if successful, None otherwise.
    """
    try:
        print("🔄 Setting up QA chain...")
        
        # Initialize embeddings
        embeddings = GoogleGenerativeAIEmbeddings(model="models/text-embedding-004")
        
        # Load existing vector store
        print(f"📂 Loading vector database from '{db_path}'...")
        vectorstore = Chroma(
            persist_directory=db_path,
            embedding_function=embeddings
        )
        
        # Initialize the language model
        print("🤖 Initializing Gemini model...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-pro",
            temperature=0.3,
            convert_system_message_to_human=True
        )
        
        # Create retriever
        retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5}
        )
        
        # Create QA chain
        qa_chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=False
        )
        
        print("✅ QA chain setup complete!")
        return qa_chain
        
    except Exception as e:
        print(f"❌ Error setting up QA chain: {str(e)}")
        return None

def test_qa_system(qa_chain: RetrievalQA):
    """Test the QA system with a sample Schema Therapy analysis prompt."""
    
    # Define the test prompt
    test_prompt = """
    You are a Schema Therapy expert assistant. Your role is to analyze user statements and identify potential core schemas based ONLY on the provided source context from Schema Therapy literature.

    User Statement: "I constantly feel inadequate and flawed around people. I feel like I'm about to make a mistake at any moment."

    Please analyze this statement and:
    1. Identify which core schemas this statement might point to
    2. Explain your reasoning step-by-step in clear, understandable language
    3. Base your analysis strictly on the Schema Therapy concepts found in the source documents
    4. Do not provide clinical diagnoses - focus on educational schema identification
    5. If the source material doesn't contain enough information, clearly state this limitation

    Provide a comprehensive but accessible analysis.
    """
    
    print("\n" + "="*80)
    print("🧠 SCHEMA THERAPY ANALYSIS TEST")
    print("="*80)
    print("\n🔍 Analyzing user statement with Schema Therapy framework...")
    print("\n📝 User Statement: 'I constantly feel inadequate and flawed around people. I feel like I'm about to make a mistake at any moment.'\n")
    
    try:
        # Get response from QA chain
        response = qa_chain.run(test_prompt)
        
        print("🎯 AI ANALYSIS RESULTS:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")

def main():
    """Main execution function."""
    print("🚀 Schema Therapy RAG Engine Starting...")
    print("="*60)
    
    # Setup environment
    if not setup_environment():
        print("❌ Environment setup failed. Please configure your Google API key.")
        return
    
    # Check if vector database already exists
    if os.path.exists(VECTOR_DB_PATH):
        print(f"📂 Found existing vector database at '{VECTOR_DB_PATH}'")
        print("🔄 Skipping document processing and using existing database...")
    else:
        print(f"📂 Vector database not found at '{VECTOR_DB_PATH}'")
        print("🔄 Creating new vector database...")
        
        # Load documents
        combined_text = load_documents_from_folder(SOURCE_FOLDER)
        
        if not combined_text.strip():
            print("❌ No documents found or processed. Please check your source folder.")
            return
        
        # Create vector database
        if not create_vector_database(combined_text, VECTOR_DB_PATH):
            print("❌ Failed to create vector database.")
            return
    
    # Setup QA chain
    qa_chain = setup_qa_chain(VECTOR_DB_PATH)
    
    if qa_chain is None:
        print("❌ Failed to setup QA chain.")
        return
    
    # Test the system
    test_qa_system(qa_chain)
    
    print("\n✅ Schema Therapy RAG Engine completed successfully!")
    print("💡 You can now modify the test_prompt in the code to analyze different statements.")

if __name__ == "__main__":
    main()
