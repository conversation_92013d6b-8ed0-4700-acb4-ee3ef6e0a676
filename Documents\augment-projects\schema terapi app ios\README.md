# Schema Therapy RAG Engine

A Python-based Retrieval-Augmented Generation (RAG) system for analyzing user statements using Schema Therapy principles from your private document collection.

## Features

- 📄 **Document Processing**: Automatically extracts text from PDF and DOCX files
- 🧠 **AI-Powered Analysis**: Uses Google's Gemini Pro model for Schema Therapy analysis
- 💾 **Persistent Vector Database**: ChromaDB for efficient document retrieval
- 🔍 **Smart Retrieval**: Finds the most relevant Schema Therapy concepts for analysis
- 🎯 **Expert Analysis**: Provides step-by-step reasoning based on your source documents

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install google-generativeai langchain-google-genai langchain chromadb pypdf2 python-docx
```

### 2. Get Google API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Configure the Script

1. Open `main.py`
2. Replace `"YOUR_GOOGLE_API_KEY"` with your actual API key:
   ```python
   GOOGLE_API_KEY = "your_actual_api_key_here"
   ```

### 4. Prepare Your Documents

1. Create a folder named `kaynaklarim` in the same directory as `main.py`
2. Add your Schema Therapy documents (PDF and DOCX files) to this folder

### 5. Run the Script

```bash
python main.py
```

## How It Works

1. **Document Loading**: The script scans the `kaynaklarim` folder for PDF and DOCX files
2. **Text Extraction**: Extracts and combines text from all documents
3. **Vector Database Creation**: Creates embeddings and stores them in ChromaDB (only on first run)
4. **QA Chain Setup**: Initializes the Gemini model and retrieval system
5. **Analysis**: Tests the system with a sample Schema Therapy analysis

## Folder Structure

```
schema-therapy-rag/
├── main.py              # Main script
├── requirements.txt     # Python dependencies
├── README.md           # This file
├── kaynaklarim/        # Your Schema Therapy documents (create this)
│   ├── document1.pdf
│   ├── document2.docx
│   └── ...
└── chroma_db/          # Vector database (created automatically)
```

## Customization

### Analyzing Different Statements

Modify the `test_prompt` in the `test_qa_system()` function to analyze different user statements.

### Adjusting Retrieval Parameters

In the `setup_qa_chain()` function, you can modify:
- `search_kwargs={"k": 5}` - Number of relevant chunks to retrieve
- `temperature=0.3` - Model creativity (0.0 = deterministic, 1.0 = creative)

### Text Chunking Parameters

In the `create_vector_database()` function:
- `chunk_size=1000` - Size of each text chunk
- `chunk_overlap=200` - Overlap between chunks

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure you've replaced the placeholder API key with your actual key
2. **No Documents Found**: Ensure the `kaynaklarim` folder exists and contains PDF/DOCX files
3. **Import Errors**: Run `pip install -r requirements.txt` to install all dependencies

### Performance Tips

- The vector database is created only once and reused on subsequent runs
- Larger documents will take longer to process initially
- Consider using smaller chunk sizes for more precise retrieval

## Security Note

- Keep your Google API key secure and never commit it to version control
- Consider using environment variables for production deployments
